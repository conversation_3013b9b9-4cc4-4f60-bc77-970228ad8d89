# frozen_string_literal: true

require "bundler/gem_tasks"
require "sentry/test/rake_tasks"

# Define spec tasks using shared rake tasks
Sentry::Test::RakeTasks.define_spec_tasks(
  spec_pattern: "spec/sentry/**/*_spec.rb",
  spec_rspec_opts: "--order rand",
  isolated_specs_pattern: "spec/isolated/**/*_spec.rb",
  isolated_rspec_opts: "--format progress"
)

# Define versioned specs task
Sentry::Test::RakeTasks.define_versioned_specs_task

task default: [:spec, :"spec:versioned", :"spec:isolated"]
