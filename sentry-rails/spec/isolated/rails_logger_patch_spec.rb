# frozen_string_literal: true

begin
  require "simplecov"
  # isolated tests need a SimpleCov name otherwise they will overwrite coverage
  SimpleCov.command_name "RailsLoggerPatch"
rescue LoadError
  # SimpleCov not available, skip coverage
end

require "logger"
require "sentry-ruby"
require "sentry/test_helper"

# Mock Rails module and logger for testing
module Rails
  class << self
    attr_accessor :logger
  end
end

def perform_basic_setup
  Sentry.init do |config|
    config.sdk_logger = Logger.new(nil)
    config.dsn = Sentry::TestHelper::DUMMY_DSN
    config.transport.transport_class = Sentry::DummyTransport
    # so the events will be sent synchronously for testing
    config.background_worker_threads = 0
    yield(config) if block_given?
  end
end

def sentry_logs
  Sentry.get_current_client.transport.envelopes
    .flat_map(&:items)
    .select { |item| item.headers[:type] == "log" }
    .flat_map { |item| item.payload[:items] }
end

RSpec.describe "Rails.logger with :logger patch" do
  include Sentry::TestHelper

  # Set up a Rails-like logger
  let(:log_output) { StringIO.new }

  before do
    Rails.logger = Logger.new(log_output)
  end

  context "when :logger patch is enabled" do
    before do
      perform_basic_setup do |config|
        config.enable_logs = true
        config.enabled_patches = [:logger]
        config.max_log_events = 10
      end
    end

    it "captures Rails.logger calls when :logger patch is enabled" do
      # Test different log levels (debug, info, warn, error, fatal)
      Rails.logger.debug("Test debug message")
      Rails.logger.info("Test info message")
      Rails.logger.warn("Test warning message")
      Rails.logger.error("Test error message")
      Rails.logger.fatal("Test fatal message")

      # Force flush the log buffer to make logs available for testing
      Sentry.get_current_client.log_event_buffer.flush

      expect(sentry_logs).not_to be_empty
      expect(sentry_logs.size).to eq(5)

      # Check that all log levels are captured correctly
      log_levels = sentry_logs.map { |log| log[:level] }
      expect(log_levels).to contain_exactly("debug", "info", "warn", "error", "fatal")

      # Check that messages are captured correctly
      log_messages = sentry_logs.map { |log| log[:body] }
      expect(log_messages).to contain_exactly(
        "Test debug message",
        "Test info message",
        "Test warning message",
        "Test error message",
        "Test fatal message"
      )
    end



    it "captures Rails.logger calls with block syntax" do
      Rails.logger.info { "Block message" }

      # Force flush the log buffer
      Sentry.get_current_client.log_event_buffer.flush

      expect(sentry_logs).not_to be_empty
      expect(sentry_logs.size).to eq(1)
      expect(sentry_logs.first[:level]).to eq("info")
      expect(sentry_logs.first[:body]).to eq("Block message")
    end

    it "captures Rails.logger calls with progname" do
      Rails.logger.info("MyProgram") { "Message with progname" }

      # Force flush the log buffer
      Sentry.get_current_client.log_event_buffer.flush

      expect(sentry_logs).not_to be_empty
      expect(sentry_logs.size).to eq(1)
      expect(sentry_logs.first[:level]).to eq("info")
      expect(sentry_logs.first[:body]).to eq("Message with progname")
    end

    it "does not capture Sentry SDK internal logs" do
      # This should not create a recursive logging situation
      Rails.logger.info(Sentry::Logger::PROGNAME) { "Internal Sentry message" }

      # Force flush the log buffer
      Sentry.get_current_client.log_event_buffer.flush

      expect(sentry_logs).to be_empty
    end

    it "strips whitespace from log messages" do
      Rails.logger.info("  Message with whitespace  ")

      # Force flush the log buffer
      Sentry.get_current_client.log_event_buffer.flush

      expect(sentry_logs).not_to be_empty
      expect(sentry_logs.first[:body]).to eq("Message with whitespace")
    end

    it "handles non-string log messages" do
      Rails.logger.info(12345)

      # Force flush the log buffer
      Sentry.get_current_client.log_event_buffer.flush

      expect(sentry_logs).not_to be_empty
      expect(sentry_logs.first[:body]).to eq("12345")
    end
  end

  context "when Rails.logger is a BroadcastLogger", skip: !defined?(ActiveSupport::BroadcastLogger) do
    let(:string_io1) { StringIO.new }
    let(:string_io2) { StringIO.new }
    let(:logger1) { Logger.new(string_io1) }
    let(:logger2) { Logger.new(string_io2) }
    let(:broadcast_logger) { ActiveSupport::BroadcastLogger.new(logger1, logger2) }

    before do
      perform_basic_setup do |config|
        config.enable_logs = true
        config.enabled_patches = [:logger]
        config.max_log_events = 10
      end
      Rails.logger = broadcast_logger
    end

    it "captures logs from BroadcastLogger" do
      Rails.logger.info("Broadcast message")

      # Force flush the log buffer
      Sentry.get_current_client.log_event_buffer.flush

      expect(sentry_logs).not_to be_empty
      expect(sentry_logs.first[:level]).to eq("info")
      expect(sentry_logs.first[:body]).to eq("Broadcast message")

      # Verify the message was also written to both underlying loggers
      expect(string_io1.string).to include("Broadcast message")
      expect(string_io2.string).to include("Broadcast message")
    end
  end
end
