# frozen_string_literal: true

require "rake/clean"
require "rspec/core/rake_task"

module Sentry
  module Test
    module RakeTasks
      extend Rake::DSL

      # Define common spec tasks that can be used across sentry gems
      def self.define_spec_tasks(options = {})
        # Default options
        opts = {
          isolated_specs_pattern: "spec/isolated/**/*_spec.rb",
          isolated_specs_dir: "spec/isolated/*",
          spec_pattern: nil,
          spec_exclude_pattern: nil,
          spec_rspec_opts: nil,
          use_rspec_for_isolated: true
        }.merge(options)

        # Define the main spec task
        RSpec::Core::RakeTask.new(:spec).tap do |task|
          task.pattern = opts[:spec_pattern] if opts[:spec_pattern]
          task.exclude_pattern = opts[:spec_exclude_pattern] if opts[:spec_exclude_pattern]
          task.rspec_opts = opts[:spec_rspec_opts] if opts[:spec_rspec_opts]
        end

        # Define isolated specs task
        if opts[:use_rspec_for_isolated]
          # Use RSpec::Core::RakeTask for isolated specs (sentry-ruby style)
          RSpec::Core::RakeTask.new(:isolated_specs).tap do |task|
            task.pattern = opts[:isolated_specs_pattern]
          end
        else
          # Use custom task that runs isolated specs with bundle exec ruby (sentry-rails style)
          task :isolated_specs do
            Dir[opts[:isolated_specs_dir]].each do |file|
              sh "bundle exec ruby #{file}"
            end
          end
        end
      end

      # Define versioned specs task (sentry-rails specific)
      def self.define_versioned_specs_task(options = {})
        opts = {
          rspec_opts: "--order rand"
        }.merge(options)

        namespace :spec do
          RSpec::Core::RakeTask.new(:versioned).tap do |task|
            ruby_ver_dir = RUBY_VERSION.split(".")[0..1].join(".")
            matching_dir = Dir["spec/versioned/*"].detect { |dir| File.basename(dir) <= ruby_ver_dir }

            unless matching_dir
              puts "No versioned specs found for ruby #{RUBY_VERSION}"
              exit 0
            end

            puts "Running versioned specs from #{matching_dir} for ruby #{RUBY_VERSION}"

            task.rspec_opts = opts[:rspec_opts]
            task.pattern = "#{matching_dir}/**/*_spec.rb"
          end
        end
      end
    end
  end
end
